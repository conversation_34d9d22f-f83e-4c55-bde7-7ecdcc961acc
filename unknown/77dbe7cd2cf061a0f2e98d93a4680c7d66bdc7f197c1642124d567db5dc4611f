import React from 'react';
import { 
  BoltIcon, 
  CalculatorIcon, 
  GlobeAltIcon, 
  ChartBarIcon,
  CurrencyDollarIcon,
  TrophyIcon 
} from '@heroicons/react/24/outline';

const QuickTests = ({ onTest, disabled }) => {
  const quickTests = [
    {
      name: 'Compound Interest',
      icon: CalculatorIcon,
      params: {
        query: 'compound interest calculation',
        top_k: 5,
        score_threshold: 0.7,
        include_content: true,
        max_content_length: 500
      },
      description: 'Find questions about compound interest calculations'
    },
    {
      name: 'Nepali Math (ब्याज गणना)',
      icon: GlobeAltIcon,
      params: {
        query: 'ब्याज गणना',
        top_k: 5,
        score_threshold: 0.7,
        include_content: true,
        max_content_length: 500
      },
      description: 'Search for interest calculations in Nepali'
    },
    {
      name: 'Mathematical Sequence',
      icon: ChartBarIcon,
      params: {
        query: 'mathematical sequence arithmetic geometric',
        top_k: 5,
        score_threshold: 0.7,
        include_content: true,
        max_content_length: 500
      },
      description: 'Find sequence and series problems'
    },
    {
      name: 'Currency Exchange',
      icon: CurrencyDollarIcon,
      params: {
        query: 'currency exchange rate conversion',
        top_k: 5,
        score_threshold: 0.7,
        include_content: true,
        max_content_length: 500
      },
      description: 'Search for currency conversion problems'
    },
    {
      name: 'Best Match (Any Topic)',
      icon: TrophyIcon,
      params: {
        query: 'investment profit loss percentage',
        top_k: 1,
        score_threshold: 0.0,
        include_content: true,
        max_content_length: 500
      },
      description: 'Get the single best match regardless of score'
    },
    {
      name: 'LaTeX Math Formula',
      icon: CalculatorIcon,
      params: {
        query: 'P(1+R/100)^T formula compound interest principal amount',
        top_k: 3,
        score_threshold: 0.6,
        include_content: true,
        max_content_length: 800
      },
      description: 'Find questions with mathematical formulas'
    }
  ];

  return (
    <div className="card">
      <div className="card-header">
        <h3 className="text-lg font-semibold flex items-center">
          <BoltIcon className="h-5 w-5 mr-2" />
          Quick Test Queries
        </h3>
      </div>
      
      <div className="p-6">
        <div className="grid grid-cols-1 gap-3">
          {quickTests.map((test, index) => {
            const IconComponent = test.icon;
            return (
              <button
                key={index}
                onClick={() => onTest(test.params)}
                disabled={disabled}
                className="flex items-start space-x-3 p-4 text-left bg-gray-50 hover:bg-gray-100 rounded-lg transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed group"
              >
                <div className="flex-shrink-0 p-2 bg-white rounded-lg group-hover:bg-primary-50 transition-colors duration-200">
                  <IconComponent className="h-5 w-5 text-primary-600" />
                </div>
                
                <div className="flex-1 min-w-0">
                  <h4 className="font-medium text-gray-900 group-hover:text-primary-700 transition-colors duration-200">
                    {test.name}
                  </h4>
                  <p className="text-sm text-gray-600 mt-1">
                    {test.description}
                  </p>
                  <div className="flex flex-wrap gap-2 mt-2 text-xs">
                    <span className="bg-white px-2 py-1 rounded text-gray-600">
                      top_k: {test.params.top_k}
                    </span>
                    <span className="bg-white px-2 py-1 rounded text-gray-600">
                      threshold: {test.params.score_threshold}
                    </span>
                  </div>
                </div>
              </button>
            );
          })}
        </div>
        
        <div className="mt-4 p-3 bg-yellow-50 rounded-lg text-xs text-yellow-700">
          <p className="font-medium mb-1">⚡ Quick Test Info:</p>
          <p>These predefined queries help you test different aspects of the search API quickly. Each test is optimized for specific use cases and demonstrates different parameter combinations.</p>
        </div>
      </div>
    </div>
  );
};

export default QuickTests;
