import React, { useState, useEffect } from 'react';
import { Toaster } from 'react-hot-toast';
import Header from './components/Header';
import SearchForm from './components/SearchForm';
import SearchResults from './components/SearchResults';
import ApiStatus from './components/ApiStatus';
import QuickTests from './components/QuickTests';
import { searchQuestions, testApiConnection } from './services/api';
import toast from 'react-hot-toast';

function App() {
  const [searchResults, setSearchResults] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [apiStatus, setApiStatus] = useState('disconnected');
  const [searchTime, setSearchTime] = useState(null);
  const [apiUrl, setApiUrl] = useState('http://172.16.16.148:8089');

  // Test API connection on mount and when URL changes
  useEffect(() => {
    checkApiConnection();
  }, [apiUrl]);

  const checkApiConnection = async () => {
    try {
      setApiStatus('testing');
      const isConnected = await testApiConnection(apiUrl);
      setApiStatus(isConnected ? 'connected' : 'disconnected');
      
      if (isConnected) {
        toast.success('API connected successfully!');
      } else {
        toast.error('Failed to connect to API');
      }
    } catch (error) {
      setApiStatus('disconnected');
      toast.error('API connection failed');
    }
  };

  const handleSearch = async (searchParams) => {
    setIsLoading(true);
    setSearchResults(null);
    
    const startTime = Date.now();
    
    try {
      const results = await searchQuestions(apiUrl, searchParams);
      const endTime = Date.now();
      
      setSearchResults(results);
      setSearchTime({
        client: endTime - startTime,
        server: results.search_time_ms
      });
      
      if (results.total_results === 0) {
        toast('No results found. Try adjusting your search parameters.', {
          icon: '🔍',
        });
      } else {
        toast.success(`Found ${results.total_results} results!`);
      }
    } catch (error) {
      console.error('Search error:', error);
      toast.error(`Search failed: ${error.message}`);
      setSearchResults(null);
    } finally {
      setIsLoading(false);
    }
  };

  const handleQuickTest = (testParams) => {
    handleSearch(testParams);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      <Toaster 
        position="top-right"
        toastOptions={{
          duration: 4000,
          style: {
            background: '#363636',
            color: '#fff',
          },
        }}
      />
      
      <Header />
      
      <div className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Left Panel - Search Interface */}
          <div className="space-y-6">
            <ApiStatus 
              status={apiStatus}
              apiUrl={apiUrl}
              onUrlChange={setApiUrl}
              onTest={checkApiConnection}
            />
            
            <SearchForm 
              onSearch={handleSearch}
              isLoading={isLoading}
              disabled={apiStatus !== 'connected'}
            />
            
            <QuickTests 
              onTest={handleQuickTest}
              disabled={isLoading || apiStatus !== 'connected'}
            />
          </div>
          
          {/* Right Panel - Results */}
          <div className="space-y-6">
            <SearchResults 
              results={searchResults}
              isLoading={isLoading}
              searchTime={searchTime}
              apiUrl={apiUrl}
            />
          </div>
        </div>
      </div>
    </div>
  );
}

export default App;
