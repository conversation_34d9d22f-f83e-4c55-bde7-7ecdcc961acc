version: '3.8'

services:
  # React Frontend (Production)
  frontend:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: discussion_forum_frontend
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - REACT_APP_API_URL=http://*************:8089
      - REACT_APP_API_TIMEOUT=30000
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3000/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - frontend_network

  # Development service (alternative to production)
  frontend-dev:
    build:
      context: .
      dockerfile: Dockerfile.dev
    container_name: discussion_forum_frontend_dev
    ports:
      - "3001:3000"
    environment:
      - NODE_ENV=development
      - REACT_APP_API_URL=http://*************:8089
      - REACT_APP_API_TIMEOUT=30000
      - CHOKIDAR_USEPOLLING=true
      - WATCHPACK_POLLING=true
      - FAST_REFRESH=false
    volumes:
      - .:/app
      - /app/node_modules
    restart: unless-stopped
    networks:
      - frontend_network
    profiles:
      - dev

networks:
  frontend_network:
    driver: bridge
