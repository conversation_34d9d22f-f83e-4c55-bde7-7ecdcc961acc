version: '3.8'

services:
  rag-api:
    build: .
    container_name: forum
    ports:
      - "8089:8089"
    environment:
      - QDRANT_HOST=*************
      - QDRANT_PORT=6333
      - QDRANT_COLLECTION=ag_discussion_forum
      - QDRANT_TIMEOUT=60
      - HOST=0.0.0.0
      - PORT=8089
      - DEBUG=false
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - DEFAULT_TOP_K=5
      - DEFAULT_SCORE_THRESHOLD=0.7
      - RATE_LIMIT_REQUESTS=100
    restart: unless-stopped
    volumes:
      - ./questions_clean.csv:/app/questions_clean.csv:ro
      - ./.env:/app/.env:ro
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8089/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - api_network

networks:
  api_network:
    driver: bridge