# Frontend Docker Setup

This directory contains Docker configuration for the Discussion Forum RAG API frontend.

## 🐳 Docker Files

- **`Dockerfile`** - Production build with optimized React app
- **`Dockerfile.dev`** - Development build with hot reload
- **`docker-compose.yml`** - Complete setup with both production and dev services
- **`.dockerignore`** - Files to exclude from Docker build
- **`docker-entrypoint.sh`** - Entry point script for containers

## 🚀 Quick Start

### Production Mode (Default)

```bash
# Build and run production frontend
docker-compose up --build

# Or run in background
docker-compose up -d --build
```

**Access:** http://localhost:3000

### Development Mode

```bash
# Run development server with hot reload
docker-compose --profile dev up --build frontend-dev

# Or run in background
docker-compose --profile dev up -d --build frontend-dev
```

**Access:** http://localhost:3001

## 🔧 Configuration

### Environment Variables

| Variable | Default | Description |
|----------|---------|-------------|
| `REACT_APP_API_URL` | `http://*************:8089` | Backend API URL |
| `REACT_APP_API_TIMEOUT` | `30000` | API request timeout (ms) |
| `NODE_ENV` | `production` | Environment mode |

### Ports

- **Production**: `3000:3000`
- **Development**: `3001:3000`

## 📦 Build Process

### Production Build
1. Install dependencies
2. Build optimized React app
3. Serve static files with `serve`

### Development Build
1. Install all dependencies (including dev)
2. Mount source code as volume
3. Run `npm start` with hot reload

## 🔍 Health Checks

Both containers include health checks:
- **Endpoint**: `http://localhost:3000/`
- **Interval**: 30 seconds
- **Timeout**: 10 seconds
- **Retries**: 3

## 🛠️ Commands

### Build Only
```bash
# Production image
docker build -t discussion-forum-frontend .

# Development image
docker build -f Dockerfile.dev -t discussion-forum-frontend:dev .
```

### Run Individual Containers
```bash
# Production
docker run -p 3000:3000 \
  -e REACT_APP_API_URL=http://*************:8089 \
  discussion-forum-frontend

# Development
docker run -p 3001:3000 \
  -e NODE_ENV=development \
  -e REACT_APP_API_URL=http://*************:8089 \
  -v $(pwd):/app \
  -v /app/node_modules \
  discussion-forum-frontend:dev
```

### Logs
```bash
# View logs
docker-compose logs frontend

# Follow logs
docker-compose logs -f frontend

# Development logs
docker-compose logs -f frontend-dev
```

### Stop Services
```bash
# Stop all services
docker-compose down

# Stop and remove volumes
docker-compose down -v

# Stop development service
docker-compose --profile dev down
```

## 🔧 Troubleshooting

### Common Issues

1. **Port Already in Use**
   - Change port mapping in `docker-compose.yml`
   - Stop conflicting services

2. **API Connection Failed**
   - Verify `REACT_APP_API_URL` is correct
   - Ensure backend API is running
   - Check network connectivity

3. **Build Failures**
   - Clear Docker cache: `docker system prune`
   - Rebuild without cache: `docker-compose build --no-cache`

4. **Development Hot Reload Not Working**
   - Ensure volume mounts are correct
   - Check `CHOKIDAR_USEPOLLING=true` is set

### Debug Commands
```bash
# Enter running container
docker exec -it discussion_forum_frontend sh

# Check container logs
docker logs discussion_forum_frontend

# Inspect container
docker inspect discussion_forum_frontend
```

## 🌐 Network

The frontend connects to the backend API at `http://*************:8089` by default. Since both services are on the same server, no special networking configuration is needed.

## 📊 Performance

### Production Optimizations
- Multi-stage build for smaller image size
- Static file serving with `serve`
- Non-root user for security
- Health checks for reliability

### Development Features
- Hot reload for fast development
- Volume mounting for live code changes
- Full development dependencies
- Polling for file system compatibility

## 🔒 Security

- Runs as non-root user (`reactuser`)
- Minimal attack surface
- No unnecessary packages in production
- Proper file permissions
