import React, { useState } from 'react';
import { 
  MagnifyingGlassIcon, 
  AdjustmentsHorizontalIcon,
  InformationCircleIcon 
} from '@heroicons/react/24/outline';

const SearchForm = ({ onSearch, isLoading, disabled }) => {
  const [formData, setFormData] = useState({
    query: 'compound interest calculation',
    top_k: 5,
    score_threshold: 0.7,
    include_content: true,
    max_content_length: 500
  });

  const [showAdvanced, setShowAdvanced] = useState(false);

  const handleSubmit = (e) => {
    e.preventDefault();
    if (!formData.query.trim()) return;
    onSearch(formData);
  };

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  return (
    <div className="card">
      <div className="card-header">
        <h3 className="text-lg font-semibold flex items-center">
          <MagnifyingGlassIcon className="h-5 w-5 mr-2" />
          Search Parameters
        </h3>
      </div>
      
      <div className="p-6">
        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Query Input */}
          <div>
            <label htmlFor="query" className="block text-sm font-medium text-gray-700 mb-2">
              Search Query
            </label>
            <textarea
              id="query"
              value={formData.query}
              onChange={(e) => handleInputChange('query', e.target.value)}
              className="input-field resize-none"
              rows={4}
              placeholder="Enter your search query (supports English, Nepali, LaTeX)..."
              disabled={disabled}
            />
            <p className="text-xs text-gray-500 mt-1">
              Supports semantic search with multilingual content and mathematical notation
            </p>
          </div>

          {/* Basic Parameters */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label htmlFor="top_k" className="block text-sm font-medium text-gray-700 mb-2">
                Top K Results
              </label>
              <input
                type="number"
                id="top_k"
                value={formData.top_k}
                onChange={(e) => handleInputChange('top_k', parseInt(e.target.value))}
                className="input-field"
                min="1"
                max="50"
                disabled={disabled}
              />
            </div>
            
            <div>
              <label htmlFor="score_threshold" className="block text-sm font-medium text-gray-700 mb-2">
                Score Threshold
              </label>
              <input
                type="number"
                id="score_threshold"
                value={formData.score_threshold}
                onChange={(e) => handleInputChange('score_threshold', parseFloat(e.target.value))}
                className="input-field"
                min="0"
                max="1"
                step="0.1"
                disabled={disabled}
              />
            </div>
          </div>

          {/* Advanced Options Toggle */}
          <button
            type="button"
            onClick={() => setShowAdvanced(!showAdvanced)}
            className="flex items-center text-sm text-primary-600 hover:text-primary-700"
          >
            <AdjustmentsHorizontalIcon className="h-4 w-4 mr-1" />
            {showAdvanced ? 'Hide' : 'Show'} Advanced Options
          </button>

          {/* Advanced Options */}
          {showAdvanced && (
            <div className="space-y-4 p-4 bg-gray-50 rounded-lg">
              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="include_content"
                  checked={formData.include_content}
                  onChange={(e) => handleInputChange('include_content', e.target.checked)}
                  className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                  disabled={disabled}
                />
                <label htmlFor="include_content" className="text-sm font-medium text-gray-700">
                  Include Content
                </label>
                <div className="group relative">
                  <InformationCircleIcon className="h-4 w-4 text-gray-400 cursor-help" />
                  <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-800 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                    Include full question content in response
                  </div>
                </div>
              </div>
              
              <div>
                <label htmlFor="max_content_length" className="block text-sm font-medium text-gray-700 mb-2">
                  Max Content Length
                </label>
                <input
                  type="number"
                  id="max_content_length"
                  value={formData.max_content_length}
                  onChange={(e) => handleInputChange('max_content_length', parseInt(e.target.value))}
                  className="input-field"
                  min="100"
                  max="2000"
                  disabled={disabled}
                />
                <p className="text-xs text-gray-500 mt-1">
                  Maximum characters for content preview (100-2000)
                </p>
              </div>
            </div>
          )}

          {/* Submit Button */}
          <button
            type="submit"
            disabled={disabled || isLoading || !formData.query.trim()}
            className="btn-primary w-full flex items-center justify-center space-x-2"
          >
            {isLoading ? (
              <>
                <div className="loading-spinner"></div>
                <span>Searching...</span>
              </>
            ) : (
              <>
                <MagnifyingGlassIcon className="h-5 w-5" />
                <span>Search Questions</span>
              </>
            )}
          </button>
        </form>

        {/* Parameter Info */}
        <div className="mt-4 p-3 bg-blue-50 rounded-lg text-xs text-blue-700">
          <p className="font-medium mb-1">💡 Search Tips:</p>
          <ul className="space-y-1">
            <li>• Use score_threshold=0.0 with top_k=1 to get the best match regardless of similarity</li>
            <li>• Higher thresholds (0.8+) return only very similar questions</li>
            <li>• Lower thresholds (0.5-) return more diverse but potentially less relevant results</li>
            <li>• The system understands context and meaning, not just keywords</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default SearchForm;
