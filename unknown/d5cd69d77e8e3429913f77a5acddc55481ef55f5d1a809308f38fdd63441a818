import React from 'react';
import { MagnifyingGlassIcon, CpuChipIcon } from '@heroicons/react/24/outline';

const Header = () => {
  return (
    <header className="bg-white shadow-soft border-b border-gray-100">
      <div className="container mx-auto px-4 py-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <div className="p-2 bg-gradient-to-r from-primary-500 to-secondary-500 rounded-lg">
                <MagnifyingGlassIcon className="h-6 w-6 text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gradient">
                  Discussion Forum RAG API
                </h1>
                <p className="text-gray-600 text-sm">
                  Interactive Testing Interface
                </p>
              </div>
            </div>
          </div>
          
          <div className="flex items-center space-x-4">
            <div className="hidden md:flex items-center space-x-2 text-sm text-gray-500">
              <CpuChipIcon className="h-4 w-4" />
              <span>Powered by NEXTAI <img src="/next_ai_asia_logo.png" alt="Logo" style="height: 20px; vertical-align: middle;"/></span>
            </div>
            
            <a
              href="http://*************:8089/docs"
              target="_blank"
              rel="noopener noreferrer"
              className="btn-outline text-sm"
            >
              📚 API Docs
            </a>
          </div>
        </div>
        
        <div className="mt-4 flex flex-wrap gap-2 text-xs text-gray-500">
          <span className="bg-gray-100 px-2 py-1 rounded">
            🔍 Semantic Search
          </span>
          <span className="bg-gray-100 px-2 py-1 rounded">
            🌐 Multilingual Support
          </span>
          <span className="bg-gray-100 px-2 py-1 rounded">
            📊 Real-time Results
          </span>
          <span className="bg-gray-100 px-2 py-1 rounded">
            🧮 LaTeX Math Support
          </span>
        </div>
      </div>
    </header>
  );
};

export default Header;
