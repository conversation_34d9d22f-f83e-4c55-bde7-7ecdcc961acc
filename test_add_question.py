#!/usr/bin/env python3
"""
Test script for the add_question API endpoint
"""

import requests
import json

# API base URL
API_URL = "http://localhost:8089"

def test_create_collection():
    """Test creating an empty collection"""
    print("Testing collection creation...")
    
    response = requests.post(f"{API_URL}/create_collection")
    print(f"Status: {response.status_code}")
    print(f"Response: {response.json()}")
    print()

def test_add_question_basic():
    """Test adding a basic question with only required fields"""
    print("Testing basic question addition...")
    
    data = {
        "question_id": 1001,
        "question": "What is the derivative of $f(x) = x^2 + 3x + 2$?"
    }
    
    response = requests.post(f"{API_URL}/add_question", json=data)
    print(f"Status: {response.status_code}")
    print(f"Response: {json.dumps(response.json(), indent=2)}")
    print()

def test_add_question_with_dynamic_fields():
    """Test adding a question with dynamic fields"""
    print("Testing question addition with dynamic fields...")
    
    data = {
        "question_id": 1002,
        "question": "Calculate the integral of $\\int x^2 dx$",
        "type": "calculation",
        "subject": "calculus",
        "difficulty": "beginner",
        "topic": "integration",
        "tags": ["math", "calculus", "integration"],
        "author": "teacher123",
        "created_at": "2025-01-07T10:00:00Z",
        "points": 10,
        "estimated_time": "5 minutes"
    }
    
    response = requests.post(f"{API_URL}/add_question", json=data)
    print(f"Status: {response.status_code}")
    print(f"Response: {json.dumps(response.json(), indent=2)}")
    print()

def test_add_question_physics():
    """Test adding a physics question"""
    print("Testing physics question addition...")
    
    data = {
        "question_id": 1003,
        "question": "A ball is thrown upward with initial velocity $v_0 = 20 m/s$. What is the maximum height reached?",
        "type": "problem_solving",
        "subject": "physics",
        "difficulty": "intermediate",
        "topic": "kinematics",
        "tags": ["physics", "mechanics", "projectile_motion"],
        "author": "physics_teacher",
        "grade_level": "high_school",
        "curriculum": "AP Physics",
        "answer_type": "numerical"
    }
    
    response = requests.post(f"{API_URL}/add_question", json=data)
    print(f"Status: {response.status_code}")
    print(f"Response: {json.dumps(response.json(), indent=2)}")
    print()

def test_search_questions():
    """Test searching for added questions"""
    print("Testing question search...")
    
    data = {
        "query": "derivative calculus",
        "top_k": 3
    }
    
    response = requests.post(f"{API_URL}/search", json=data)
    print(f"Status: {response.status_code}")
    print(f"Response: {json.dumps(response.json(), indent=2)}")
    print()

if __name__ == "__main__":
    print("Testing Add Question API Endpoint")
    print("=" * 50)
    
    try:
        # Test collection creation
        test_create_collection()
        
        # Test adding questions with different field combinations
        test_add_question_basic()
        test_add_question_with_dynamic_fields()
        test_add_question_physics()
        
        # Test searching
        test_search_questions()
        
    except requests.exceptions.ConnectionError:
        print("Error: Could not connect to API server.")
        print("Make sure the server is running on http://localhost:8089")
    except Exception as e:
        print(f"Error: {e}")
