#!/usr/bin/env python3
"""
RAG System for Questions Dataset using OpenAI text-embedding-3-large and Qdrant Vector Database

This script implements a RAG system that preserves LaTeX content and uses OpenAI's 
text-embedding-3-large model for creating embeddings.
"""

import pandas as pd
import numpy as np
import os
from typing import List, Dict, Any, Optional
import openai
from qdrant_client import QdrantClient
from qdrant_client.models import PointStruct, VectorParams, Distance
import time
import argparse


class QuestionRAGOpenAI:
    """RAG system using OpenAI embeddings that preserves LaTeX content."""

    def __init__(self, openai_api_key: Optional[str] = None, qdrant_client: Optional[QdrantClient] = None, collection_name: str = "ag_discussion_forum"):
        """
        Initialize the RAG system with OpenAI embeddings.

        Args:
            openai_api_key: OpenAI API key (if not provided, will look for OPENAI_API_KEY env var)
            qdrant_client: Existing Qdrant client (if not provided, creates in-memory client)
            collection_name: Name for the Qdrant collection
        """
        print("Initializing RAG system with OpenAI embeddings...")

        # Initialize OpenAI client
        if openai_api_key:
            self.openai_client = openai.Client(api_key=openai_api_key)
        else:
            # Try to get from environment variable
            api_key = os.getenv('OPENAI_API_KEY')
            if not api_key:
                raise ValueError("OpenAI API key not provided. Set OPENAI_API_KEY environment variable or pass it as parameter.")
            self.openai_client = openai.Client(api_key=api_key)

        self.collection_name = collection_name

        # Use provided client or create new one
        if qdrant_client:
            self.client = qdrant_client
        else:
            self.client = QdrantClient(":memory:")  # Use in-memory storage

        # Check if collection exists and get its dimensions
        try:
            collection_info = self.client.get_collection(self.collection_name)
            self.embedding_dim = collection_info.config.params.vectors.size
            print(f"Found existing collection '{self.collection_name}' with dimension {self.embedding_dim}")

            # Set the appropriate model based on dimension
            if self.embedding_dim == 1536:
                self.embedding_model = "text-embedding-3-large"
                print("Using text-embedding-3-large model with 1536 dimensions (matches existing collection)")
            else:
                print(f"Warning: Unusual embedding dimension {self.embedding_dim}")

        except Exception as e:
            # Collection doesn't exist, create new one with text-embedding-3-large
            self.embedding_model = "text-embedding-3-large"
            self.embedding_dim = 1536   

            try:
                self.client.create_collection(
                    collection_name=self.collection_name,
                    vectors_config=VectorParams(
                        size=self.embedding_dim,
                        distance=Distance.COSINE
                    )
                )
                print(f"Created new collection '{self.collection_name}' with text-embedding-3-large (1536 dimensions)")
            except Exception as create_error:
                print(f"Error creating collection: {create_error}")
                raise

        print(f"RAG system initialized with model: {self.embedding_model}, dimension: {self.embedding_dim}")
    
    def create_openai_embeddings(self, texts: List[str], batch_size: int = 100) -> List[List[float]]:
        """
        Create embeddings using OpenAI's embedding model (auto-detected based on collection).

        Args:
            texts: List of texts to embed
            batch_size: Number of texts to process in each API call

        Returns:
            List of embedding vectors
        """
        embeddings = []
        total_texts = len(texts)
        
        print(f"Creating OpenAI embeddings for {total_texts} texts...")
        
        for i in range(0, total_texts, batch_size):
            batch = texts[i:i + batch_size]
            
            try:
                # Call OpenAI API with dimensions=1536 to match collection
                response = self.openai_client.embeddings.create(
                    model=self.embedding_model,
                    input=batch,
                    dimensions=1536  # Explicitly request 1536 dimensions
                )
                
                # Extract embeddings from response
                batch_embeddings = [data.embedding for data in response.data]
                embeddings.extend(batch_embeddings)
                
                print(f"Processed {min(i + batch_size, total_texts)} / {total_texts} texts")
                
                # Add small delay to respect rate limits
                time.sleep(0.1)
                
            except Exception as e:
                print(f"Error creating embeddings for batch {i//batch_size + 1}: {e}")
                raise
        
        return embeddings
    
    def ingest_questions(self, df: pd.DataFrame, batch_size: int = 50):
        """
        Ingest questions into Qdrant vector database using OpenAI embeddings.
        
        Args:
            df: DataFrame with 'id' and 'body' columns
            batch_size: Batch size for API calls and Qdrant uploads
        """
        print("Preparing questions for embedding...")
        
        # Remove null values
        df_clean = df.dropna().copy()
        print(f"Processing {len(df_clean)} questions (after removing null values)")
        
        # Use original body content without any processing to preserve LaTeX
        texts = df_clean['body'].tolist()
        
        print("Creating OpenAI embeddings...")
        start_time = time.time()
        
        # Create embeddings using OpenAI
        embeddings = self.create_openai_embeddings(texts, batch_size=batch_size)
        
        print(f"Embeddings created in {time.time() - start_time:.2f} seconds")
        print("Uploading to Qdrant...")
        
        # Prepare points for upload
        points = []
        for idx, row in df_clean.iterrows():
            point = PointStruct(
                id=int(row['id']),  # Use the original question ID as reference
                vector=embeddings[len(points)],  # Use index in embeddings list
                payload={
                    "question_id": int(row['id']),
                    "original_body": row['body'],  # Store original LaTeX content
                    "text_length": len(row['body'])
                }
            )
            points.append(point)
        
        # Upload to Qdrant in batches
        upload_batch_size = 100
        for i in range(0, len(points), upload_batch_size):
            batch_points = points[i:i + upload_batch_size]
            self.client.upsert(
                collection_name=self.collection_name,
                points=batch_points
            )
            print(f"Uploaded batch {i // upload_batch_size + 1} / {(len(points) + upload_batch_size - 1) // upload_batch_size}")
        
        print(f"Successfully ingested {len(points)} questions into Qdrant with OpenAI embeddings!")
        return len(points)
    
    def search_related_questions(self, query: str, top_k: int = 5, score_threshold: float = 0.7) -> List[Dict[str, Any]]:
        """
        Search for related questions using OpenAI embeddings.
        
        Args:
            query: Search query
            top_k: Number of results to return
            score_threshold: Minimum similarity score
            
        Returns:
            List of similar questions with metadata
        """
        # Create embedding for the query using OpenAI
        query_response = self.openai_client.embeddings.create(
            model=self.embedding_model,
            input=[query],
            dimensions=1536  # Explicitly request 1536 dimensions
        )
        query_embedding = query_response.data[0].embedding

        # Search using Qdrant
        search_results = self.client.query_points(
            collection_name=self.collection_name,
            query=query_embedding,
            limit=top_k,
            score_threshold=score_threshold
        )

        # Format results
        results = []
        for result in search_results.points:
            results.append({
                "question_id": result.payload["question_id"],
                "similarity_score": result.score,
                "original_body": result.payload["original_body"],
                "text_length": result.payload["text_length"]
            })

        return results
    
    def display_search_results(self, results: List[Dict[str, Any]], query: str):
        """Display search results in a formatted way without visual indicators."""
        print(f"\nSearch Query: '{query}'")
        print(f"Found {len(results)} related questions:\n")
        
        for i, result in enumerate(results, 1):
            print(f"{'='*60}")
            print(f"Result #{i}")
            print(f"Question ID: {result['question_id']}")
            print(f"Similarity Score: {result['similarity_score']:.4f}")
            print(f"Text Length: {result['text_length']} characters")
            print(f"\nQuestion Content (LaTeX preserved):")
            # Show first 500 characters to avoid overwhelming output
            content = result['original_body']
            if len(content) > 500:
                print(content[:500] + "...")
            else:
                print(content)
            print()


def main():
    """Main function to run the OpenAI RAG system."""
    parser = argparse.ArgumentParser(description='RAG System using OpenAI text-embedding-3-large')
    parser.add_argument('--csv_file', default='questions.csv', help='Path to the questions CSV file')
    parser.add_argument('--sample_size', type=int, default=100, help='Number of questions to process (0 for all)')
    parser.add_argument('--interactive', action='store_true', help='Run in interactive mode')
    parser.add_argument('--api_key', help='OpenAI API key (optional if OPENAI_API_KEY env var is set)')
    
    args = parser.parse_args()
    
    try:
        # Initialize RAG system
        rag = QuestionRAGOpenAI(openai_api_key=args.api_key)
        
        # Load data
        print(f"Loading questions from {args.csv_file}...")
        df = pd.read_csv(args.csv_file)
        
        # Use sample if specified
        if args.sample_size > 0:
            df = df.head(args.sample_size)
            print(f"Using sample of {len(df)} questions")
        else:
            print(f"Using all {len(df)} questions")
        
        # Ingest questions
        rag.ingest_questions(df)
        
        if args.interactive:
            # Interactive mode
            print("\nInteractive Question Search")
            print("Enter your query to find related questions (or 'quit' to exit)")
            print("-" * 50)
            
            while True:
                query = input("\nEnter your search query: ").strip()
                
                if query.lower() in ['quit', 'exit', 'q']:
                    print("Goodbye!")
                    break
                
                if not query:
                    print("Please enter a valid query.")
                    continue
                
                try:
                    results = rag.search_related_questions(query, top_k=3)
                    if results:
                        rag.display_search_results(results, query)
                    else:
                        print(f"No related questions found for: '{query}'")
                        print("Try a different query or lower the similarity threshold.")
                except Exception as e:
                    print(f"Error during search: {e}")
        else:
            # Demo mode
            demo_queries = [
                "compound interest calculation",
                "ब्याज गणना",
                "currency exchange rate",
                "mathematical sequence"
            ]
            
            print("\nRunning demo searches...")
            for query in demo_queries:
                try:
                    results = rag.search_related_questions(query, top_k=3)
                    rag.display_search_results(results, query)
                except Exception as e:
                    print(f"Error searching for '{query}': {e}")
    
    except Exception as e:
        print(f"Error: {e}")
        print("Make sure you have set your OpenAI API key either as an environment variable or pass it with --api_key")


if __name__ == "__main__":
    main()
