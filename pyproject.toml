[project]
name = "discussion-forum-aroma"
version = "0.1.0"
description = "RAG API for Discussion Forum with Qdrant Vector Store"
readme = "README.md"
requires-python = ">=3.13"
dependencies = [
    "beautifulsoup4>=4.13.4",
    "fastapi>=0.115.0",
    "ipykernel>=6.29.5",
    "openai>=1.86.0",
    "openpyxl>=3.1.5",
    "pandas>=2.3.0",
    "pydantic>=2.10.0",
    "python-dotenv>=1.0.0",
    "python-multipart>=0.0.12",
    "qdrant-client>=1.14.2",
    "slowapi>=0.1.9",
    "uvicorn[standard]>=0.32.0",
]
