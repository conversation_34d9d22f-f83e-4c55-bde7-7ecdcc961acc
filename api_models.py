#!/usr/bin/env python3
"""
Pydantic models for API request and response validation
"""

from typing import List, Optional, Any, Dict
from pydantic import BaseModel, Field, validator


class HealthResponse(BaseModel):
    """Health check response model."""
    status: str = Field(..., description="Health status")
    message: str = Field(..., description="Health message")
    timestamp: str = Field(..., description="Response timestamp")
    version: str = Field(..., description="API version")


class SearchRequest(BaseModel):
    """Search request model."""
    query: str = Field(..., min_length=1, description="Search query text (automatically truncated to 1000 characters)")
    top_k: Optional[int] = Field(default=5, ge=1, le=50, description="Number of results to return")
    score_threshold: Optional[float] = Field(
        default=0.7,
        ge=0.0,
        le=1.0,
        description="Minimum similarity score threshold"
    )
    include_content: Optional[bool] = Field(
        default=True,
        description="Whether to include full question content in results"
    )
    max_content_length: Optional[int] = Field(
        default=500,
        ge=100,
        le=2000,
        description="Maximum content length to return (characters)"
    )

    # Private field to track original query length for truncation detection
    _original_query_length: Optional[int] = None

    @validator('query')
    def validate_query(cls, v, values):
        """Validate query is not empty after stripping and truncate if too long."""
        if not v.strip():
            raise ValueError('Query cannot be empty')

        # Store original length before truncation
        stripped_query = v.strip()
        values['_original_query_length'] = len(stripped_query)

        # Truncate query to 1000 characters if it's too long
        if len(stripped_query) > 1000:
            return stripped_query[:1000]

        return stripped_query

    def was_query_truncated(self) -> bool:
        """Check if the query was truncated during validation."""
        return self._original_query_length is not None and self._original_query_length > 1000


class QuestionResult(BaseModel):
    """Individual question result model."""
    question_id: int = Field(..., description="Unique question identifier")
    similarity_score: float = Field(..., description="Similarity score (0-1)")
    question: str = Field(..., description="Question content (LaTeX preserved)")
    text_length: int = Field(..., description="Original text length in characters")

    class Config:
        extra = "allow"  # Allow additional dynamic fields in search results


class SearchResponse(BaseModel):
    """Search response model."""
    query: str = Field(..., description="Original search query")
    total_results: int = Field(..., description="Number of results found")
    results: List[QuestionResult] = Field(..., description="List of matching questions")
    search_time_ms: float = Field(..., description="Search execution time in milliseconds")
    parameters: Dict[str, Any] = Field(..., description="Search parameters used")


class QuestionDetailResponse(BaseModel):
    """Detailed question response model."""
    question_id: int = Field(..., description="Unique question identifier")
    content: str = Field(..., description="Full question content (LaTeX preserved)")
    text_length: int = Field(..., description="Text length in characters")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional metadata")



class AddQuestionRequest(BaseModel):
    """Request model for adding a new question to the collection."""
    question_id: int = Field(..., description="Unique question identifier")
    question: str = Field(..., min_length=1, description="Question content (LaTeX preserved)")

    class Config:
        extra = "allow"  # Allow additional fields beyond the defined ones

    @validator('question')
    def validate_question(cls, v):
        """Validate question is not empty after stripping."""
        if not v.strip():
            raise ValueError('Question cannot be empty')
        return v.strip()

    def get_additional_fields(self) -> Dict[str, Any]:
        """Get all additional fields beyond question_id and question."""
        return {k: v for k, v in self.dict().items() if k not in ['question_id', 'question']}


class AddQuestionResponse(BaseModel):
    """Response model for adding a question."""
    success: bool = Field(..., description="Whether the operation was successful")
    question_id: int = Field(..., description="Question ID that was added")
    message: str = Field(..., description="Success message")
    embedding_created: bool = Field(..., description="Whether embedding was created successfully")
    stored_fields: Dict[str, Any] = Field(..., description="All fields that were stored in Qdrant")


class ErrorResponse(BaseModel):
    """Error response model."""
    error: str = Field(..., description="Error type")
    message: str = Field(..., description="Error message")
    detail: Optional[str] = Field(None, description="Detailed error information")
    timestamp: str = Field(..., description="Error timestamp")
