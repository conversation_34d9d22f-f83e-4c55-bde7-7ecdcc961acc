# Discussion Forum RAG API Frontend

A modern React frontend with Tailwind CSS for testing and interacting with the Discussion Forum RAG API.

## 🚀 Features

- **Modern UI**: Clean, responsive design with Tailwind CSS
- **Real-time Search**: Interactive search interface with live results
- **API Testing**: Built-in tools for testing different search scenarios
- **Multilingual Support**: Handles English, Nepali, and mixed content
- **LaTeX Support**: Proper display of mathematical notation
- **Quick Tests**: Predefined queries for rapid API testing
- **Result Analysis**: Detailed similarity scores and performance metrics
- **Copy Functions**: Easy copying of question IDs and content
- **Error Handling**: Comprehensive error messages and status indicators

## 📋 Prerequisites

- Node.js 16+ and npm/yarn
- Discussion Forum RAG API running (default: http://*************:8089)

## 🛠️ Installation

1. **Navigate to frontend directory:**
   ```bash
   cd frontend
   ```

2. **Install dependencies:**
   ```bash
   npm install
   # or
   yarn install
   ```

3. **Install additional Tailwind plugins:**
   ```bash
   npm install @tailwindcss/forms @tailwindcss/typography
   ```

## 🚀 Running the Application

1. **Start the development server:**
   ```bash
   npm start
   # or
   yarn start
   ```

2. **Open your browser:**
   - Navigate to http://localhost:3000
   - The app will automatically reload when you make changes

## 🔧 Configuration

### API URL Configuration
- Default API URL: `http://*************:8089`
- Change the URL in the API Status section of the interface
- The URL is saved in localStorage for persistence

### Proxy Configuration
The `package.json` includes a proxy setting for development:
```json
"proxy": "http://*************:8089"
```

## 📱 Usage

### 1. API Connection
- Check the API Status panel to ensure connection
- Test the connection using the "Test" button
- Update the API URL if needed

### 2. Search Interface
- Enter your search query in the text area
- Adjust parameters:
  - **Top K**: Number of results (1-50)
  - **Score Threshold**: Minimum similarity (0.0-1.0)
  - **Include Content**: Whether to return full content
  - **Max Content Length**: Preview length limit

### 3. Quick Tests
Use predefined test queries:
- **Compound Interest**: Financial calculations
- **Nepali Math**: ब्याज गणना (Interest in Nepali)
- **Mathematical Sequence**: Arithmetic/geometric series
- **Currency Exchange**: Exchange rate problems
- **Best Match**: Get single best result (score=0.0)
- **LaTeX Math**: Mathematical formulas

### 4. Results Analysis
- View similarity scores and rankings
- Copy question IDs for reference
- View full question content in modal
- Analyze search performance metrics

## 🎨 UI Components

### Header
- Branding and navigation
- Link to API documentation
- Feature highlights

### API Status
- Connection status indicator
- URL configuration
- Connection testing

### Search Form
- Query input with validation
- Parameter controls
- Advanced options toggle

### Quick Tests
- Predefined test scenarios
- One-click testing
- Parameter previews

### Search Results
- Result cards with similarity scores
- Performance metrics
- Content preview/full view
- Copy functionality

## 🔍 Search Tips

1. **Semantic Search**: The system understands meaning, not just keywords
2. **Score Thresholds**:
   - 0.9+: Nearly identical content
   - 0.8-0.9: Highly similar
   - 0.7-0.8: Moderately similar (recommended)
   - 0.6-0.7: Somewhat related
   - 0.0-0.6: Loosely related

3. **Best Match Strategy**: Use `score_threshold=0.0` with `top_k=1`
4. **Multilingual**: Works with English, Nepali, and mixed content
5. **Mathematical Content**: Supports LaTeX notation and formulas

## 🛠️ Development

### Project Structure
```
frontend/
├── public/
│   └── index.html
├── src/
│   ├── components/
│   │   ├── Header.js
│   │   ├── ApiStatus.js
│   │   ├── SearchForm.js
│   │   ├── QuickTests.js
│   │   └── SearchResults.js
│   ├── services/
│   │   └── api.js
│   ├── App.js
│   ├── index.js
│   └── index.css
├── package.json
├── tailwind.config.js
└── postcss.config.js
```

### Key Technologies
- **React 18**: Modern React with hooks
- **Tailwind CSS**: Utility-first CSS framework
- **Heroicons**: Beautiful SVG icons
- **Axios**: HTTP client for API calls
- **React Hot Toast**: Toast notifications

### Styling
- Custom Tailwind configuration with brand colors
- Responsive design for mobile/desktop
- Smooth animations and transitions
- Glass morphism effects

## 🚀 Building for Production

1. **Build the application:**
   ```bash
   npm run build
   # or
   yarn build
   ```

2. **Serve the build:**
   ```bash
   npx serve -s build
   ```

## 🔧 Troubleshooting

### Common Issues

1. **API Connection Failed**
   - Verify API is running on specified URL
   - Check CORS configuration
   - Ensure network connectivity

2. **Search Returns No Results**
   - Lower the score threshold
   - Try different keywords
   - Check if collection has data

3. **Build Errors**
   - Clear node_modules and reinstall
   - Check Node.js version compatibility
   - Verify all dependencies are installed

### Debug Mode
- Open browser developer tools
- Check console for error messages
- Monitor network requests in Network tab

## 📝 API Integration

The frontend integrates with these API endpoints:
- `GET /health` - Connection testing
- `POST /search` - Question search
- `GET /question/{id}` - Question details
- `GET /stats` - Collection statistics
- `GET /collections/info` - Collection info

## 🎯 Performance

- Optimized for fast loading
- Efficient re-rendering with React hooks
- Lazy loading for large content
- Responsive design for all devices

## 📄 License

This project is part of the Discussion Forum RAG API system.
