import axios from 'axios';

// Create axios instance with default config
const createApiClient = (baseURL) => {
  return axios.create({
    baseURL,
    timeout: 30000,
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    },
  });
};

// Test API connection
export const testApiConnection = async (apiUrl) => {
  try {
    const client = createApiClient(apiUrl);
    const response = await client.get('/health');
    return response.status === 200;
  } catch (error) {
    console.error('API connection test failed:', error);
    return false;
  }
};

// Search questions
export const searchQuestions = async (apiUrl, searchParams) => {
  try {
    const client = createApiClient(apiUrl);
    const response = await client.post('/search', searchParams);
    return response.data;
  } catch (error) {
    if (error.response) {
      // Server responded with error status
      const errorMessage = error.response.data?.detail || 
                          error.response.data?.message || 
                          `HTTP ${error.response.status}`;
      throw new Error(errorMessage);
    } else if (error.request) {
      // Request was made but no response received
      throw new Error('No response from server. Check if API is running.');
    } else {
      // Something else happened
      throw new Error(error.message || 'Unknown error occurred');
    }
  }
};

// Get question details by ID
export const getQuestionById = async (apiUrl, questionId) => {
  try {
    const client = createApiClient(apiUrl);
    const response = await client.get(`/question/${questionId}`);
    return response.data;
  } catch (error) {
    if (error.response) {
      const errorMessage = error.response.data?.detail || 
                          error.response.data?.message || 
                          `HTTP ${error.response.status}`;
      throw new Error(errorMessage);
    } else if (error.request) {
      throw new Error('No response from server. Check if API is running.');
    } else {
      throw new Error(error.message || 'Unknown error occurred');
    }
  }
};

// Get collection statistics
export const getCollectionStats = async (apiUrl) => {
  try {
    const client = createApiClient(apiUrl);
    const response = await client.get('/stats');
    return response.data;
  } catch (error) {
    if (error.response) {
      const errorMessage = error.response.data?.detail || 
                          error.response.data?.message || 
                          `HTTP ${error.response.status}`;
      throw new Error(errorMessage);
    } else if (error.request) {
      throw new Error('No response from server. Check if API is running.');
    } else {
      throw new Error(error.message || 'Unknown error occurred');
    }
  }
};

// Get collection info
export const getCollectionInfo = async (apiUrl) => {
  try {
    const client = createApiClient(apiUrl);
    const response = await client.get('/collections/info');
    return response.data;
  } catch (error) {
    if (error.response) {
      const errorMessage = error.response.data?.detail || 
                          error.response.data?.message || 
                          `HTTP ${error.response.status}`;
      throw new Error(errorMessage);
    } else if (error.request) {
      throw new Error('No response from server. Check if API is running.');
    } else {
      throw new Error(error.message || 'Unknown error occurred');
    }
  }
};

// Create embedding for text
export const createEmbedding = async (apiUrl, text) => {
  try {
    const client = createApiClient(apiUrl);
    const response = await client.post('/embed', { text });
    return response.data;
  } catch (error) {
    if (error.response) {
      const errorMessage = error.response.data?.detail || 
                          error.response.data?.message || 
                          `HTTP ${error.response.status}`;
      throw new Error(errorMessage);
    } else if (error.request) {
      throw new Error('No response from server. Check if API is running.');
    } else {
      throw new Error(error.message || 'Unknown error occurred');
    }
  }
};

// Utility function to format error messages
export const formatErrorMessage = (error) => {
  if (typeof error === 'string') {
    return error;
  }
  
  if (error.response?.data?.detail) {
    // Handle FastAPI validation errors
    if (Array.isArray(error.response.data.detail)) {
      return error.response.data.detail
        .map(err => `${err.loc?.join('.')} - ${err.msg}`)
        .join('; ');
    }
    return error.response.data.detail;
  }
  
  return error.message || 'Unknown error occurred';
};
