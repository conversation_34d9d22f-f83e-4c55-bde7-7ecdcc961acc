import React, { useState } from 'react';
import { 
  ClockIcon, 
  DocumentTextIcon, 
  EyeIcon,
  ClipboardDocumentIcon,
  ChartBarIcon,
  ExclamationTriangleIcon 
} from '@heroicons/react/24/outline';
import { getQuestionById } from '../services/api';
import toast from 'react-hot-toast';

const SearchResults = ({ results, isLoading, searchTime, apiUrl }) => {
  const [expandedQuestion, setExpandedQuestion] = useState(null);
  const [loadingQuestion, setLoadingQuestion] = useState(null);

  const getSimilarityBadgeClass = (score) => {
    if (score >= 0.8) return 'similarity-badge similarity-high';
    if (score >= 0.6) return 'similarity-badge similarity-medium';
    return 'similarity-badge similarity-low';
  };

  const formatScore = (score) => {
    return `${(score * 100).toFixed(1)}%`;
  };

  const copyToClipboard = (text) => {
    navigator.clipboard.writeText(text).then(() => {
      toast.success('Copied to clipboard!');
    }).catch(() => {
      toast.error('Failed to copy to clipboard');
    });
  };

  const handleViewFullQuestion = async (questionId) => {
    setLoadingQuestion(questionId);
    try {
      const questionData = await getQuestionById(apiUrl, questionId);
      setExpandedQuestion(questionData);
    } catch (error) {
      toast.error(`Failed to load question: ${error.message}`);
    } finally {
      setLoadingQuestion(null);
    }
  };

  const renderContent = (content, isPreview = false) => {
    if (!content) return null;
    
    // Check if content contains LaTeX or mathematical notation
    const hasLatex = content.includes('\\') || content.includes('$') || content.includes('mathrm');
    
    return (
      <div className={`mt-3 p-4 bg-gray-50 rounded-lg border-l-4 border-primary-300 ${hasLatex ? 'math-content' : ''}`}>
        <pre className="whitespace-pre-wrap text-sm text-gray-700 font-mono leading-relaxed">
          {content}
        </pre>
      </div>
    );
  };

  if (isLoading) {
    return (
      <div className="card">
        <div className="p-8 text-center">
          <div className="loading-spinner mx-auto mb-4"></div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">Searching Questions...</h3>
          <p className="text-gray-600">
            Processing your query with semantic search
          </p>
        </div>
      </div>
    );
  }

  if (!results) {
    return (
      <div className="card">
        <div className="p-8 text-center">
          <DocumentTextIcon className="h-16 w-16 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Ready to Search</h3>
          <p className="text-gray-600 mb-4">
            Enter a search query to find related questions from the discussion forum.
          </p>
          <div className="text-sm text-gray-500 space-y-1">
            <p>🔍 Semantic search with OpenAI embeddings</p>
            <p>🌐 Supports English, Nepali, and mixed content</p>
            <p>🧮 Understands mathematical notation and LaTeX</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Search Statistics */}
      <div className="card">
        <div className="p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-gray-900 flex items-center">
              <ChartBarIcon className="h-5 w-5 mr-2" />
              Search Results
            </h3>
            {searchTime && (
              <div className="flex items-center text-sm text-gray-500">
                <ClockIcon className="h-4 w-4 mr-1" />
                {searchTime.server}ms server + {searchTime.client}ms client
              </div>
            )}
          </div>
          
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
              <div>
                <div className="text-2xl font-bold text-blue-600">{results.total_results}</div>
                <div className="text-sm text-blue-700">Results Found</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-blue-600">{results.parameters.top_k}</div>
                <div className="text-sm text-blue-700">Requested</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-blue-600">{results.parameters.score_threshold}</div>
                <div className="text-sm text-blue-700">Threshold</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-blue-600">{results.search_time_ms}ms</div>
                <div className="text-sm text-blue-700">Search Time</div>
              </div>
            </div>
          </div>
          
          <div className="mt-3 text-sm text-gray-600">
            <strong>Query:</strong> "{results.query}"
          </div>
        </div>
      </div>

      {/* No Results */}
      {results.total_results === 0 && (
        <div className="card">
          <div className="p-8 text-center">
            <ExclamationTriangleIcon className="h-16 w-16 text-yellow-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No Results Found</h3>
            <p className="text-gray-600 mb-4">
              No questions found matching your criteria.
            </p>
            <div className="text-sm text-gray-500 space-y-1">
              <p>💡 Try lowering the score threshold</p>
              <p>🔄 Use different or broader keywords</p>
              <p>📊 Check if the collection has data</p>
              <p>🌐 Try searching in different languages</p>
            </div>
          </div>
        </div>
      )}

      {/* Results List */}
      {results.results && results.results.map((result, index) => (
        <div key={result.question_id} className="result-card animate-slide-up">
          <div className="flex items-start justify-between mb-4">
            <div className="flex items-center space-x-3">
              <span className="text-lg font-bold text-gray-400">#{index + 1}</span>
              <div>
                <span className={getSimilarityBadgeClass(result.similarity_score)}>
                  {formatScore(result.similarity_score)} Match
                </span>
                <span className="ml-2 text-sm bg-gray-100 text-gray-700 px-2 py-1 rounded">
                  ID: {result.question_id}
                </span>
              </div>
            </div>
            
            <div className="flex items-center space-x-2">
              <button
                onClick={() => handleViewFullQuestion(result.question_id)}
                disabled={loadingQuestion === result.question_id}
                className="btn-outline text-xs"
              >
                {loadingQuestion === result.question_id ? (
                  <div className="loading-spinner h-3 w-3"></div>
                ) : (
                  <>
                    <EyeIcon className="h-3 w-3 mr-1" />
                    View Full
                  </>
                )}
              </button>
              
              <button
                onClick={() => copyToClipboard(result.question_id.toString())}
                className="btn-secondary text-xs"
              >
                <ClipboardDocumentIcon className="h-3 w-3 mr-1" />
                Copy ID
              </button>
            </div>
          </div>
          
          <div className="text-sm text-gray-600 mb-3">
            <span>Length: {result.text_length.toLocaleString()} characters</span>
            {result.is_truncated && (
              <span className="ml-2 text-yellow-600">• Content truncated for display</span>
            )}
          </div>
          
          {(result.content || result.content_preview) && renderContent(
            result.content || result.content_preview,
            !result.content
          )}
        </div>
      ))}

      {/* Expanded Question Modal */}
      {expandedQuestion && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-xl shadow-large max-w-4xl w-full max-h-[90vh] overflow-hidden">
            <div className="flex items-center justify-between p-6 border-b border-gray-200">
              <h3 className="text-lg font-semibold">
                Question Details - ID: {expandedQuestion.question_id}
              </h3>
              <button
                onClick={() => setExpandedQuestion(null)}
                className="text-gray-400 hover:text-gray-600"
              >
                ✕
              </button>
            </div>
            
            <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
              <div className="mb-4 text-sm text-gray-600">
                <strong>Length:</strong> {expandedQuestion.text_length.toLocaleString()} characters
              </div>
              
              {renderContent(expandedQuestion.content)}
              
              <div className="mt-4 flex space-x-2">
                <button
                  onClick={() => copyToClipboard(expandedQuestion.content)}
                  className="btn-secondary text-sm"
                >
                  <ClipboardDocumentIcon className="h-4 w-4 mr-1" />
                  Copy Content
                </button>
                
                <button
                  onClick={() => copyToClipboard(expandedQuestion.question_id.toString())}
                  className="btn-outline text-sm"
                >
                  <ClipboardDocumentIcon className="h-4 w-4 mr-1" />
                  Copy ID
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SearchResults;
