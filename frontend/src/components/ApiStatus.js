import React from 'react';
import { 
  CheckCircleIcon, 
  XCircleIcon, 
  ArrowPathIcon,
  ServerIcon 
} from '@heroicons/react/24/outline';

const ApiStatus = ({ status, apiUrl, onUrlChange, onTest }) => {
  const getStatusIcon = () => {
    switch (status) {
      case 'connected':
        return <CheckCircleIcon className="h-5 w-5 text-green-500" />;
      case 'disconnected':
        return <XCircleIcon className="h-5 w-5 text-red-500" />;
      case 'testing':
        return <ArrowPathIcon className="h-5 w-5 text-blue-500 animate-spin" />;
      default:
        return <ServerIcon className="h-5 w-5 text-gray-400" />;
    }
  };

  const getStatusText = () => {
    switch (status) {
      case 'connected':
        return 'Connected';
      case 'disconnected':
        return 'Disconnected';
      case 'testing':
        return 'Testing...';
      default:
        return 'Unknown';
    }
  };

  const getStatusColor = () => {
    switch (status) {
      case 'connected':
        return 'text-green-600 bg-green-50 border-green-200';
      case 'disconnected':
        return 'text-red-600 bg-red-50 border-red-200';
      case 'testing':
        return 'text-blue-600 bg-blue-50 border-blue-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  return (
    <div className="card">
      <div className="card-header">
        <h3 className="text-lg font-semibold flex items-center">
          <ServerIcon className="h-5 w-5 mr-2" />
          API Configuration
        </h3>
      </div>
      
      <div className="p-6 space-y-4">
        <div>
          <label htmlFor="apiUrl" className="block text-sm font-medium text-gray-700 mb-2">
            API Base URL
          </label>
          <div className="flex space-x-2">
            <input
              type="text"
              id="apiUrl"
              value={apiUrl}
              onChange={(e) => onUrlChange(e.target.value)}
              className="input-field flex-1"
              placeholder="http://172.16.16.148:8089"
            />
            <button
              onClick={onTest}
              disabled={status === 'testing'}
              className="btn-secondary whitespace-nowrap"
            >
              {status === 'testing' ? (
                <ArrowPathIcon className="h-4 w-4 animate-spin" />
              ) : (
                'Test'
              )}
            </button>
          </div>
        </div>
        
        <div className={`flex items-center space-x-2 p-3 rounded-lg border ${getStatusColor()}`}>
          {getStatusIcon()}
          <span className="font-medium">{getStatusText()}</span>
          {status === 'connected' && (
            <span className="text-sm opacity-75">
              API is ready for requests
            </span>
          )}
          {status === 'disconnected' && (
            <span className="text-sm opacity-75">
              Check URL and ensure API is running
            </span>
          )}
        </div>
        
        <div className="text-xs text-gray-500 space-y-1">
          <p>• Ensure the API server is running on the specified URL</p>
          <p>• Check that CORS is properly configured for cross-origin requests</p>
          <p>• Verify network connectivity to the API server</p>
        </div>
      </div>
    </div>
  );
};

export default ApiStatus;
