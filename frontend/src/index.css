@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
  }
  
  body {
    @apply antialiased;
  }
}

@layer components {
  .btn-primary {
    @apply bg-gradient-to-r from-primary-500 to-primary-600 hover:from-primary-600 hover:to-primary-700 text-white font-medium px-4 py-2 rounded-lg transition-all duration-200 transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
  }
  
  .btn-secondary {
    @apply bg-white hover:bg-gray-50 text-gray-700 font-medium px-4 py-2 rounded-lg border border-gray-300 transition-all duration-200 transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
  }
  
  .btn-outline {
    @apply bg-transparent hover:bg-primary-50 text-primary-600 font-medium px-4 py-2 rounded-lg border border-primary-300 transition-all duration-200 transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
  }
  
  .input-field {
    @apply w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200;
  }
  
  .card {
    @apply bg-white rounded-xl shadow-soft border border-gray-100 transition-all duration-200 hover:shadow-medium;
  }
  
  .card-header {
    @apply bg-gradient-to-r from-primary-500 to-secondary-500 text-white px-6 py-4 rounded-t-xl;
  }
  
  .result-card {
    @apply bg-white rounded-xl shadow-soft border border-gray-100 p-6 transition-all duration-200 hover:shadow-medium hover:scale-[1.02] border-l-4 border-l-primary-500;
  }
  
  .similarity-badge {
    @apply inline-flex items-center px-3 py-1 rounded-full text-sm font-medium;
  }
  
  .similarity-high {
    @apply bg-green-100 text-green-800;
  }
  
  .similarity-medium {
    @apply bg-yellow-100 text-yellow-800;
  }
  
  .similarity-low {
    @apply bg-red-100 text-red-800;
  }
  
  .code-block {
    @apply bg-gray-900 text-gray-100 p-4 rounded-lg font-mono text-sm overflow-x-auto;
  }
  
  .loading-spinner {
    @apply animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500;
  }
}

@layer utilities {
  .text-gradient {
    @apply bg-gradient-to-r from-primary-600 to-secondary-600 bg-clip-text text-transparent;
  }
  
  .bg-glass {
    @apply bg-white/80 backdrop-blur-sm;
  }
  
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  @apply bg-gray-100;
}

::-webkit-scrollbar-thumb {
  @apply bg-primary-300 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-primary-400;
}

/* Animation for result cards */
.result-enter {
  opacity: 0;
  transform: translateY(20px);
}

.result-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition: opacity 300ms, transform 300ms;
}

/* Nepali font support */
.nepali-text {
  font-family: 'Noto Sans Devanagari', 'Inter', sans-serif;
}

/* LaTeX/Math content styling */
.math-content {
  @apply font-mono text-sm bg-gray-50 p-2 rounded border-l-2 border-primary-300;
}
