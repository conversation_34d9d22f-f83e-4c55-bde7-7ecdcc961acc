#!/usr/bin/env python3
"""
Configuration settings for the Discussion Forum RAG API
"""

import os
from typing import Optional
from pydantic import BaseModel, Field


class Settings(BaseModel):
    """Application settings with environment variable support hello."""
    
    # API Settings
    app_name: str = Field(default="Discussion Forum RAG API", description="Application name")
    app_version: str = Field(default="1.0.0", description="Application version")
    debug: bool = Field(default=False, description="Debug mode")
    
    # Server Settings
    host: str = Field(default="0.0.0.0", description="Server host")
    port: int = Field(default=8089, description="Server port")
    reload: bool = Field(default=False, description="Auto-reload on code changes")
    
    # OpenAI Settings
    openai_api_key: Optional[str] = Field(default=None, description="OpenAI API key")
    openai_model: str = Field(default="text-embedding-3-large", description="OpenAI embedding model")
    openai_dimensions: int = Field(default=1536, description="Embedding dimensions")
    
    # Qdrant Settings
    qdrant_host: str = Field(default="127.0.0.1", description="Qdrant host")
    qdrant_port: int = Field(default=6333, description="Qdrant port")
    qdrant_collection: str = Field(default="ag_discussion_forum", description="Qdrant collection name")
    qdrant_timeout: int = Field(default=30, description="Qdrant client timeout in seconds")
    
    # Search Settings
    default_top_k: int = Field(default=5, description="Default number of search results")
    default_score_threshold: float = Field(default=0.7, description="Default similarity score threshold")
    max_top_k: int = Field(default=50, description="Maximum number of search results allowed")
    
    # Rate Limiting
    rate_limit_requests: int = Field(default=100, description="Rate limit: requests per minute")
    rate_limit_window: int = Field(default=60, description="Rate limit window in seconds")
    
    # CORS Settings
    cors_origins: list[str] = Field(default=["*"], description="Allowed CORS origins")
    cors_methods: list[str] = Field(default=["GET", "POST"], description="Allowed CORS methods")
    cors_headers: list[str] = Field(default=["*"], description="Allowed CORS headers")
    
    def __init__(self, **kwargs):
        # Load from environment variables
        for field_name in self.model_fields.keys():
            env_name = field_name.upper()
            env_value = os.getenv(env_name)
            if env_value is not None and field_name not in kwargs:
                field_info = self.model_fields[field_name]
                # Convert string values to appropriate types
                if field_info.annotation == bool:
                    kwargs[field_name] = env_value.lower() in ('true', '1', 'yes', 'on')
                elif field_info.annotation == int:
                    kwargs[field_name] = int(env_value)
                elif field_info.annotation == float:
                    kwargs[field_name] = float(env_value)
                elif str(field_info.annotation).startswith('list'):
                    kwargs[field_name] = env_value.split(',')
                else:
                    kwargs[field_name] = env_value
        super().__init__(**kwargs)
        
    def get_openai_api_key(self) -> str:
        """Get OpenAI API key from settings or environment."""
        if self.openai_api_key:
            return self.openai_api_key
        
        env_key = os.getenv('OPENAI_API_KEY')
        if env_key:
            return env_key
            
        raise ValueError(
            "OpenAI API key not found. Set OPENAI_API_KEY environment variable "
            "or provide it in the configuration."
        )


# Global settings instance
settings = Settings()
