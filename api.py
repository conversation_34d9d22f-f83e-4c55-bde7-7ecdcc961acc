#!/usr/bin/env python3
"""
FastAPI application for Discussion Forum RAG System
"""

import time
import logging
from datetime import datetime
from typing import List, Optional
from contextlib import asynccontextmanager

from fastapi import FastAPI, HTTPException, Depends, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from slowapi import Limiter, _rate_limit_exceeded_handler
from slowapi.util import get_remote_address
from slowapi.errors import RateLimitExceeded
from qdrant_client import QdrantClient
from qdrant_client.http.exceptions import UnexpectedResponse
from qdrant_client.models import VectorParams, Distance

from config import settings
from api_models import (
    HealthResponse, SearchRequest, SearchResponse, QuestionResult,
    QuestionDetailResponse, ErrorResponse, AddQuestionRequest, AddQuestionResponse
)
from rag_system_openai import QuestionRAGOpenAI

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Rate limiter
limiter = Limiter(key_func=get_remote_address)

# Global variables for RAG system
rag_system: Optional[QuestionRAGOpenAI] = None
qdrant_client: Optional[QdrantClient] = None


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager."""
    global rag_system, qdrant_client
    
    try:
        logger.info("Initializing RAG system...")
        
        # Initialize Qdrant client
        qdrant_client = QdrantClient(
            host=settings.qdrant_host,
            port=settings.qdrant_port,
            timeout=settings.qdrant_timeout
        )
        
        # Initialize RAG system
        rag_system = QuestionRAGOpenAI(
            openai_api_key=settings.get_openai_api_key(),
            qdrant_client=qdrant_client,
            collection_name=settings.qdrant_collection
        )
        
        logger.info("RAG system initialized successfully")
        
        # Verify collection exists
        try:
            collection_info = qdrant_client.get_collection(settings.qdrant_collection)
            logger.info(f"Connected to collection '{settings.qdrant_collection}' with {collection_info.points_count} points")
        except Exception as e:
            logger.warning(f"Could not verify collection: {e}")
        
        yield
        
    except Exception as e:
        logger.error(f"Failed to initialize RAG system: {e}")
        raise
    finally:
        logger.info("Shutting down RAG system...")


# Create FastAPI app
app = FastAPI(
    title=settings.app_name,
    version=settings.app_version,
    description="API for searching and retrieving questions using RAG with Qdrant vector store",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.cors_origins,
    allow_credentials=True,
    allow_methods=settings.cors_methods,
    allow_headers=settings.cors_headers,
)

# Add rate limiting
app.state.limiter = limiter
app.add_exception_handler(RateLimitExceeded, _rate_limit_exceeded_handler)


def get_rag_system() -> QuestionRAGOpenAI:
    """Dependency to get RAG system instance."""
    if rag_system is None:
        raise HTTPException(status_code=503, detail="RAG system not initialized")
    return rag_system


def get_qdrant_client() -> QdrantClient:
    """Dependency to get Qdrant client instance."""
    if qdrant_client is None:
        raise HTTPException(status_code=503, detail="Qdrant client not initialized")
    return qdrant_client


@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """Global exception handler."""
    logger.error(f"Unhandled exception: {exc}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content=ErrorResponse(
            error="InternalServerError",
            message="An unexpected error occurred",
            detail=str(exc) if settings.debug else None,
            timestamp=datetime.utcnow().isoformat()
        ).dict()
    )

@app.get("/health", response_model=HealthResponse)
@limiter.limit(f"{settings.rate_limit_requests}/minute")
async def health_check(request: Request):
    """Health check endpoint."""
    return HealthResponse(
        status="healthy",
        message="API is running",
        timestamp=datetime.utcnow().isoformat(),
        version=settings.app_version
    )


@app.post("/search", response_model=SearchResponse)
# @limiter.limit(f"{settings.rate_limit_requests}/minute")
async def search_questions(
    request: Request,
    search_request: SearchRequest,
    rag: QuestionRAGxOpenAI = Depends(get_rag_system)
):
    """

    ## Request Parameters

    ### query (string, required)
    - **Description**: The search query text to find related questions
    - **Format**: Plain text string (supports English, Nepali, and mixed content)
    - **Length**: Any length (automatically truncated to 1000 characters if longer)
    - **Examples**:
      - "compound interest calculation"
      - "ब्याज गणना" (Interest calculation in Nepali)
      - "mathematical sequence problems"
    - **Note**: The search is semantic, so it finds conceptually related content even
      if exact keywords don't match. Queries longer than 1000 characters are automatically
      truncated and the `query_truncated` flag in the response indicates if this occurred.

    ### top_k (integer, optional)
    - **Description**: Maximum number of similar questions to return
    - **Range**: 1-50
    - **Default**: 5
    - **Examples**:
      - `1`: Get only the most similar question
      - `5`: Get top 5 most similar questions
      - `10`: Get top 10 results for broader exploration
    - **Note**: Actual results may be fewer if score_threshold filters out low-similarity matches

    ### score_threshold (float, optional)
    - **Description**: Minimum similarity score required for results (cosine similarity)
    - **Range**: 0.0-1.0
    - **Default**: 0.7
    - **Score Meanings**:
      - `0.9-1.0`: Nearly identical or very closely related
      - `0.8-0.9`: Highly similar content
      - `0.7-0.8`: Moderately similar (good matches)
      - `0.6-0.7`: Somewhat related
      - `0.0-0.6`: Loosely related or different topics
    - **Examples**:
      - `0.0`: Return all results regardless of similarity (get highest scoring)
      - `0.7`: Only return reasonably similar questions (recommended)
      - `0.9`: Only return very closely related questions
    - **Tip**: Use 0.0 with top_k=1 to get the single best match regardless of score

    ### include_content (boolean, optional)
    - **Description**: Whether to include the full question content in response
    - **Default**: true
    - **Values**:
      - `true`: Include full question content (slower, larger response)
      - `false`: Exclude content, only metadata (faster, smaller response)
    - **Use Cases**:
      - `true`: When displaying full question details to users
      - `false`: For search suggestions, autocomplete, or mobile apps
    - **Note**: content_preview is always included regardless of this setting

    ### max_content_length (integer, optional)
    - **Description**: Maximum characters to return in content preview
    - **Range**: 100-2000
    - **Default**: 500
    - **Examples**:
      - `200`: Short preview for mobile interfaces
      - `500`: Standard preview length
      - `1000`: Longer preview for detailed views
    - **Note**: Full content (if include_content=true) may be longer than this limit

    ## Response Fields

    ### query (string)
    - **Description**: The original search query that was processed
    - **Format**: Exact copy of the input query

    ### total_results (integer)
    - **Description**: Number of questions found matching the criteria
    - **Range**: 0 to top_k value
    - **Note**: May be less than top_k if score_threshold filters out results

    ### search_time_ms (float)
    - **Description**: Time taken to execute the search in milliseconds
    - **Format**: Decimal number (e.g., 245.67)
    - **Includes**: Query embedding creation + vector search + result formatting

    ### results (array)
    - **Description**: Array of matching questions ordered by similarity score (highest first)
    - **Length**: 0 to top_k items

    #### Each result contains:

    **question_id (integer)**
    - **Description**: Unique identifier of the question in the database
    - **Format**: Positive integer
    - **Usage**: Use this ID with GET /question/{question_id} for full details

    **similarity_score (float)**
    - **Description**: Cosine similarity score between query and question
    - **Range**: 0.0-1.0 (higher = more similar)
    - **Format**: Decimal with up to 4 decimal places
    - **Interpretation**: See score_threshold parameter for score meanings

    **content (string or null)**
    - **Description**: Full question content with LaTeX and HTML preserved
    - **Conditions**:
      - Present when include_content=true
      - null when include_content=false
    - **Format**: HTML/LaTeX markup preserved as stored
    - **Length**: May be truncated at 2000 characters for very long questions

    **content_preview (string or null)**
    - **Description**: Truncated version of question content for quick preview
    - **Length**: Limited by max_content_length parameter
    - **Format**: Plain text with "..." appended if truncated
    - **Always Present**: Included regardless of include_content setting

    **text_length (integer)**
    - **Description**: Original length of the question content in characters
    - **Usage**: Indicates full size before any truncation

    **is_truncated (boolean)**
    - **Description**: Whether the content was truncated due to length limits
    - **Values**:
      - `true`: Content was shortened
      - `false`: Full content is shown

    ### parameters (object)
    - **Description**: Echo of the search parameters used for this query
    - **Purpose**: Confirms what values were actually applied (including defaults)
    - **Fields**: All input parameters with resolved default values

    ## Usage Examples

    ### Basic Search
    ```json
    {
      "query": "compound interest"
    }
    ```

    ### Get Single Best Match
    ```json
    {
      "query": "ब्याज गणना",
      "top_k": 1,
      "score_threshold": 0.0
    }
    ```

    ### High-Quality Results Only
    ```json
    {
      "query": "mathematical sequence",
      "top_k": 10,
      "score_threshold": 0.8
    }
    ```

    ### Fast Search (No Content)
    ```json
    {
      "query": "currency exchange",
      "include_content": false,
      "max_content_length": 200
    }
    ```

    ## Error Responses

    - **400 Bad Request**: Invalid parameters (empty query, top_k out of range, etc.)
    - **500 Internal Server Error**: Search system failure or OpenAI API issues
    - **503 Service Unavailable**: RAG system not initialized or Qdrant connection failed

    ## Performance Notes

    - Search time typically 200-800ms depending on query complexity
    - Larger top_k values don't significantlyresults impact performance
    - include_content=false reduces response size by ~70%
    - Lower score_threshold may return more  but doesn't affect search speed
    """
    start_time = time.time()
    
    try:
        # Query is already validated and truncated by Pydantic model
        query = search_request.query

        # Perform search
        results = rag.search_related_questions(
            query=query,
            top_k=search_request.top_k if search_request.top_k is not None else settings.default_top_k,
            score_threshold=search_request.score_threshold if search_request.score_threshold is not None else settings.default_score_threshold
        )
        
        # Format results
        formatted_results = []
        for result in results:
            content = result.get("original_body", "")
            
            # Handle content truncation
            if search_request.include_content:
                max_length = search_request.max_content_length or 500
                if len(content) > max_length:
                    content_preview = content[:max_length] + "..."
                    is_truncated = True
                    full_content = content if len(content) <= 2000 else content[:2000] + "..."
                else:
                    content_preview = content
                    is_truncated = False
                    full_content = content
            else:
                content_preview = None
                full_content = None
                is_truncated = False
            
            formatted_results.append(QuestionResult(
                question_id=result["question_id"],
                similarity_score=result["similarity_score"],
                content=full_content if search_request.include_content else None,
                content_preview=content_preview,
                text_length=result["text_length"],
                is_truncated=is_truncated
            ))
        
        search_time = (time.time() - start_time) * 1000
        
        return SearchResponse(
            query=query,  # Return the truncated query
            total_results=len(formatted_results),
            results=formatted_results,
            search_time_ms=round(search_time, 2),
            parameters={
                "top_k": search_request.top_k if search_request.top_k is not None else settings.default_top_k,
                "score_threshold": search_request.score_threshold if search_request.score_threshold is not None else settings.default_score_threshold,
                "include_content": search_request.include_content,
                "max_content_length": search_request.max_content_length,
                "query_truncated": search_request.was_query_truncated()  # Use the new method to detect truncation
            }
        )
        
    except Exception as e:
        logger.error(f"Search error: {e}")
        raise HTTPException(status_code=500, detail=f"Search failed: {str(e)}")

@app.post("/add_question", response_model=AddQuestionResponse)
async def add_question(
    request: Request,
    add_request: AddQuestionRequest,
    rag: QuestionRAGOpenAI = Depends(get_rag_system)
):
    """
    Add a new question to the Qdrant collection with dynamic fields.

    This endpoint allows you to add individual questions to the vector database.
    The question will be embedded using OpenAI's text-embedding-3-large model
    and stored in the Qdrant collection for future similarity searches.

    **Required Fields:**
    - `question_id`: Unique integer identifier for the question
    - `question`: The question content (LaTeX mathematical notation preserved)

    **Dynamic Fields:**
    You can add any additional fields directly to the request body. Common examples:
    - `type`: Question type (e.g., "multiple_choice", "essay", "calculation")
    - `subject`: Subject area (e.g., "mathematics", "physics", "chemistry")
    - `difficulty`: Difficulty level (e.g., "beginner", "intermediate", "advanced")
    - `topic`: Specific topic (e.g., "derivatives", "integration", "limits")
    - `tags`: Array of tags
    - `author`: Question author
    - `created_at`: Creation timestamp

    **Example Request:**
    ```json
    {
        "question_id": 12345,
        "question": "What is the derivative of $f(x) = x^2 + 3x + 2$?",
        "type": "calculation",
        "subject": "calculus",
        "difficulty": "beginner",
        "topic": "derivatives",
        "tags": ["math", "calculus", "derivatives"],
        "author": "teacher123"
    }
    ```

    **Returns:**
    - Success confirmation with question ID and all stored fields
    - Error details if the operation fails
    """
    start_time = time.time()

    try:
        logger.info(f"Adding question {add_request.question_id} to collection")

        # Check if question already exists
        try:
            existing_point = rag.client.retrieve(
                collection_name=rag.collection_name,
                ids=[add_request.question_id]
            )
            if existing_point:
                logger.warning(f"Question {add_request.question_id} already exists, will update")
        except Exception:
            # Point doesn't exist, which is fine
            pass

        # Get additional fields from the request
        additional_fields = add_request.get_additional_fields()

        # Add the question to the collection
        result = rag.add_single_question(
            question_id=add_request.question_id,
            question_text=add_request.question,
            additional_fields=additional_fields
        )

        if not result["success"]:
            raise HTTPException(
                status_code=500,
                detail=f"Failed to add question to collection: {result.get('error', 'Unknown error')}"
            )

        processing_time = (time.time() - start_time) * 1000
        logger.info(f"Successfully added question {add_request.question_id} in {processing_time:.2f}ms")

        return AddQuestionResponse(
            success=True,
            question_id=add_request.question_id,
            message=f"Question {add_request.question_id} successfully added to collection",
            embedding_created=True,
            stored_fields=result["payload"]
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error adding question {add_request.question_id}: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to add question: {str(e)}"
        )


@app.post("/create_collection")
@limiter.limit("5/minute")
async def create_collection(
    request: Request,
    collection_name: Optional[str] = None,
    qdrant: QdrantClient = Depends(get_qdrant_client)
):
    """
    Create an empty Qdrant collection for storing questions.

    This endpoint creates a new collection in Qdrant with the proper configuration
    for storing question embeddings using OpenAI's text-embedding-3-large model.

    **Parameters:**
    - `collection_name`: Optional collection name (defaults to 'ag_discussion')

    **Rate Limit:** 5 requests per minute per IP address

    **Returns:**
    - Success confirmation with collection details
    - Error if collection already exists or creation fails
    """
    try:
        # Use default collection name if not provided
        target_collection = collection_name or settings.qdrant_collection

        logger.info(f"Creating collection '{target_collection}'")

        # Check if collection already exists
        try:
            existing_collection = qdrant.get_collection(target_collection)
            return JSONResponse(
                status_code=200,
                content={
                    "success": True,
                    "message": f"Collection '{target_collection}' already exists",
                    "collection_name": target_collection,
                    "points_count": existing_collection.points_count,
                    "vector_size": existing_collection.config.params.vectors.size
                }
            )
        except Exception:
            # Collection doesn't exist, create it
            pass

        # Create new collection with OpenAI text-embedding-3-large configuration
        qdrant.create_collection(
            collection_name=target_collection,
            vectors_config=VectorParams(
                size=1536,  # text-embedding-3-large dimension
                distance=Distance.COSINE
            )
        )

        logger.info(f"Successfully created collection '{target_collection}'")

        return JSONResponse(
            status_code=201,
            content={
                "success": True,
                "message": f"Collection '{target_collection}' created successfully",
                "collection_name": target_collection,
                "vector_size": 1536,
                "distance_metric": "cosine",
                "points_count": 0
            }
        )

    except Exception as e:
        logger.error(f"Error creating collection: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Failed to create collection: {str(e)}"
        )
